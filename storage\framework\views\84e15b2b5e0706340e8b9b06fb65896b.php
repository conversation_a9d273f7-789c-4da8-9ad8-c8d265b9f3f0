<?php $__env->startSection('title', config('app.name', 'UniLink')); ?>

<?php $__env->startPush('styles'); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <!-- Defer non-critical JavaScript to prevent layout blocking -->
    <script defer src="<?php echo e(asset('js/post-summary-updater.js')); ?>"></script>
    <script defer src="<?php echo e(asset('js/comments.js')); ?>"></script>
    <script defer src="<?php echo e(asset('js/comment-modal.js')); ?>"></script>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

<?php $__env->stopPush(); ?>

<?php $__env->startSection('body-class', ($bodyClass ?? 'bg-gray-100') . ' overflow-hidden'); ?>

<?php $__env->startSection('navigation'); ?>
    <?php echo $__env->make('layouts.unilink-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Fixed Left Sidebar with anti-flicker optimization -->
    <div id="left-sidebar" class="hidden lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-r <?php echo e($leftBorderClass ?? 'border-gray-200'); ?> overflow-y-auto fixed left-0 top-16 bottom-0 z-40 sidebar-stable" style="opacity: 1; visibility: visible;">
        <?php echo $__env->make('layouts.unilink-left-sidebar-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>

    <!-- Fixed Right Sidebar with anti-flicker optimization -->
    <div id="right-sidebar" class="hidden lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-l border-custom-second-darkest overflow-y-auto fixed right-0 top-16 bottom-0 z-40 scrollbar-hide sidebar-stable" style="opacity: 1; visibility: visible;">
        <div class="livewire-stable">
            <?php echo $__env->make('layouts.unilink-right-sidebar-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>

    <!-- Central Content with proper margins for fixed sidebars -->
    <main class="lg:ml-70 xl:ml-80 lg:mr-70 xl:mr-80 <?php echo e($mainBgClass ?? 'bg-gray-100'); ?> min-h-screen pt-16 overflow-y-auto">
        <div class="<?php echo e($maxWidthClass ?? 'max-w-6xl'); ?> mx-auto px-4 <?php echo e($paddingClass ?? 'py-6'); ?>">
            <!-- Header (if provided) -->
            <?php if(isset($header)): ?>
                <div class="mb-6">
                    <?php echo e($header); ?>

                </div>
            <?php endif; ?>

            <!-- Main Content -->
            <?php if(isset($feedLayout) && $feedLayout): ?>
                <div class="space-y-4">
                    <?php echo e($slot); ?>

                </div>
            <?php else: ?>
                <?php echo e($slot); ?>

            <?php endif; ?>
        </div>
    </main>

    <!-- Mobile Sidebar -->
    <div class="lg:hidden">
        <?php echo $__env->make('layouts.unilink-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div x-data="{ open: false }"
         x-on:toggle-sidebar.window="open = !open"
         x-show="open"
         x-cloak
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 <?php echo e($overlayClass ?? 'bg-custom-darkest'); ?> bg-opacity-75 z-30 lg:hidden"
         @click="$dispatch('toggle-sidebar')">
    </div>

    <!-- Notification Popup -->
    <?php echo $__env->make('components.notification-popup', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Image Modal -->
    <?php echo $__env->make('components.image-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Anti-flicker JavaScript -->
    <script>
        // Prevent sidebar flickering during navigation
        document.addEventListener('DOMContentLoaded', function() {
            // Cache sidebar content to prevent re-rendering flicker
            const leftSidebar = document.getElementById('left-sidebar');
            const rightSidebar = document.getElementById('right-sidebar');

            if (leftSidebar && rightSidebar) {
                // Force immediate visibility
                leftSidebar.style.opacity = '1';
                leftSidebar.style.visibility = 'visible';
                rightSidebar.style.opacity = '1';
                rightSidebar.style.visibility = 'visible';

                // Store sidebar content in sessionStorage to prevent re-renders
                const leftContent = leftSidebar.innerHTML;
                const rightContent = rightSidebar.innerHTML;

                // Mark sidebars as loaded to prevent future flickers
                leftSidebar.setAttribute('data-loaded', 'true');
                rightSidebar.setAttribute('data-loaded', 'true');

                // Prevent Livewire from re-hydrating sidebar components unnecessarily
                if (window.Livewire) {
                    // Disable automatic re-hydration for sidebar components
                    const livewireComponents = rightSidebar.querySelectorAll('[wire\\:id]');
                    livewireComponents.forEach(component => {
                        component.setAttribute('wire:ignore', '');
                    });
                }
            }
        });

        // Prevent flicker on page transitions
        window.addEventListener('beforeunload', function() {
            const leftSidebar = document.getElementById('left-sidebar');
            const rightSidebar = document.getElementById('right-sidebar');

            if (leftSidebar && rightSidebar) {
                // Keep sidebars visible during transition
                leftSidebar.style.opacity = '1';
                rightSidebar.style.opacity = '1';
            }
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/layouts/unilink-layout.blade.php ENDPATH**/ ?>