<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        @auth
        <meta name="user-id" content="{{ auth()->id() }}">
        <meta name="user-role" content="{{ auth()->user()->role }}">
        <meta name="user-is-admin" content="{{ auth()->user()->isAdmin() ? 'true' : 'false' }}">
        <meta name="user-name" content="{{ auth()->user()->name }}">
        <meta name="user-avatar" content="{{ auth()->user()->avatar ?? '' }}">
        @endauth

        <title>@yield('title', config('app.name', 'Laravel'))</title>

        <!-- Preload critical resources -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link rel="dns-prefetch" href="https://fonts.bunny.net">

        <!-- Critical CSS and JS (load first to prevent layout shifts) -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Fonts with display=swap to prevent layout shift -->
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Additional Styles -->
        @stack('styles')

        <!-- Critical inline CSS to prevent flickering -->
        <style>
            /* Prevent initial layout shifts */
            .sidebar-stable {
                opacity: 1 !important;
                visibility: visible !important;
            }

            /* Hide elements until Alpine.js loads */
            [x-cloak] {
                display: none !important;
            }
        </style>

        <!-- Defer non-critical JavaScript to prevent layout blocking -->
        <script defer src="{{ asset('js/reactions.js') }}"></script>

        
    </head>
    <body class="font-sans antialiased @yield('body-class')">
        <div class="min-h-screen @yield('container-class', 'bg-gray-100')">
            @yield('navigation', '@include("layouts.navigation")')



            <!-- Page Heading -->
            @hasSection('header')
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        @yield('header')
                    </div>
                </header>
            @endif

            <!-- Page Content -->
            @hasSection('content')
                @yield('content')
            @else
                <main>
                    {{ $slot ?? '' }}
                </main>
            @endif
        </div>

        <!-- Additional Scripts -->
        @stack('scripts')

        <!-- Ensure DOM is ready before layout-dependent operations -->
        <script>
            // Prevent layout thrashing by ensuring DOM is ready
            document.addEventListener('DOMContentLoaded', function() {
                // Force a reflow to ensure all styles are applied
                document.body.offsetHeight;

                // Initialize any layout-dependent functionality here
                if (window.initializeLayoutDependentFeatures) {
                    window.initializeLayoutDependentFeatures();
                }
            });
        </script>
    </body>
</html>
