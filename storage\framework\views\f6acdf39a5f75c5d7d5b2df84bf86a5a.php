<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['post', 'showInline' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['post', 'showInline' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<div class="comment-section bg-white border-t border-gray-100" data-post-id="<?php echo e($post->id); ?>">
    <?php if(!$showInline): ?>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    Comments (<span id="comments-count-<?php echo e($post->id); ?>"><?php echo e($post->comments->count()); ?></span>)
                </h3>
    <?php endif; ?>

    <!-- Add Comment Form -->
    <?php if(auth()->guard()->check()): ?>
        <div class="p-4 border-b border-gray-50">
            <form class="comment-form" data-post-id="<?php echo e($post->id); ?>" data-parent-id="">
                <?php echo csrf_field(); ?>
                <div class="flex space-x-3">
                    <div class="flex-shrink-0">
                        <img class="h-10 w-10 rounded-full ring-2 ring-white shadow-sm"
                             src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e(auth()->user()->name); ?>">
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="relative">
                            <textarea name="content" rows="1"
                                      placeholder="Write a comment..."
                                      class="w-full px-4 py-3 border border-gray-200 rounded-full shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm bg-gray-50 hover:bg-white transition-colors duration-200 placeholder-gray-400"
                                      required></textarea>
                            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 transition-opacity duration-200" id="comment-submit-btn-<?php echo e($post->id); ?>">
                                <button type="submit"
                                        class="p-2 bg-custom-green text-white rounded-full hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green shadow-sm transition-all duration-200 hover:scale-105">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    <?php else: ?>
        <div class="mb-6 p-4 bg-gradient-to-r from-custom-green/5 to-blue-50 rounded-xl border border-custom-green/10 text-center">
            <div class="flex items-center justify-center mb-2">
                <svg class="w-5 h-5 text-custom-green mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <p class="text-gray-700 font-medium">Join the conversation</p>
            </div>
            <p class="text-gray-600 text-sm">
                <a href="<?php echo e(route('login')); ?>" class="text-custom-green hover:text-custom-second-darkest font-semibold hover:underline transition-colors">Sign in</a>
                to share your thoughts
            </p>
        </div>
    <?php endif; ?>

    <!-- Comments List -->
    <div class="comments-list divide-y divide-gray-100" id="comments-list-<?php echo e($post->id); ?>">
        <?php $__empty_1 = true; $__currentLoopData = $post->comments->whereNull('parent_id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <?php if (isset($component)) { $__componentOriginald908f04fb1ba83f78e519c9f401232b9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald908f04fb1ba83f78e519c9f401232b9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.comment-item','data' => ['comment' => $comment,'post' => $post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('comment-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['comment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($comment),'post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald908f04fb1ba83f78e519c9f401232b9)): ?>
<?php $attributes = $__attributesOriginald908f04fb1ba83f78e519c9f401232b9; ?>
<?php unset($__attributesOriginald908f04fb1ba83f78e519c9f401232b9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald908f04fb1ba83f78e519c9f401232b9)): ?>
<?php $component = $__componentOriginald908f04fb1ba83f78e519c9f401232b9; ?>
<?php unset($__componentOriginald908f04fb1ba83f78e519c9f401232b9); ?>
<?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="no-comments text-gray-500 text-center py-12 px-4">
                <div class="max-w-sm mx-auto">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No comments yet</h3>
                    <p class="text-gray-500">Be the first to share your thoughts!</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php if(!$showInline): ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
/* Comment Form Enhancements */
.comment-section .comment-form textarea:focus {
    min-height: 100px;
    border-radius: 1rem;
    transition: all 0.3s ease;
    background-color: white;
}

.comment-section .comment-form textarea:focus + div #comment-submit-btn-<?php echo e($post->id ?? ''); ?> {
    opacity: 1;
}

/* Reply Form Styling */
.comment-section .reply-form textarea:focus {
    min-height: 80px;
    transition: min-height 0.2s ease;
}

/* Comment Item Hover Effects */
.comment-section .comment-item {
    transition: all 0.2s ease;
}

.comment-section .comment-item:hover {
    transform: translateY(-1px);
}

/* Action Button Enhancements */
.comment-section .comment-actions button {
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.comment-section .comment-actions button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Nested Comments Styling */
.comment-section .nested-comments {
    position: relative;
}

.comment-section .nested-comments::before {
    content: '';
    position: absolute;
    left: -1px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e5e7eb, transparent);
}

/* Avatar Enhancements */
.comment-section img {
    transition: all 0.2s ease;
}

.comment-section img:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Form Input Enhancements */
.comment-section textarea {
    transition: all 0.2s ease;
}

.comment-section textarea:hover {
    border-color: #d1d5db;
}

/* Button Enhancements */
.comment-section button[type="submit"] {
    transition: all 0.2s ease;
}

.comment-section button[type="submit"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Dropdown Menu Enhancements */
.comment-section [x-show="open"] {
    backdrop-filter: blur(4px);
}

/* Loading States */
.comment-section .loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Smooth Animations */
.comment-section * {
    scroll-behavior: smooth;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced comment form interactions
    const commentForms = document.querySelectorAll('.comment-form');

    commentForms.forEach(form => {
        const textarea = form.querySelector('textarea[name="content"]');
        const submitBtn = form.querySelector('button[type="submit"]');

        if (textarea && submitBtn) {
            // Show/hide submit button based on content
            textarea.addEventListener('input', function() {
                const submitBtnContainer = form.querySelector('[id^="comment-submit-btn-"]');
                if (this.value.trim().length > 0) {
                    if (submitBtnContainer) {
                        submitBtnContainer.style.opacity = '1';
                    }
                } else {
                    if (submitBtnContainer) {
                        submitBtnContainer.style.opacity = '0';
                    }
                }
            });

            // Auto-resize textarea
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Focus effects
            textarea.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            textarea.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.parentElement.classList.remove('focused');
                }
            });
        }
    });

    // Enhanced reply form interactions
    const replyForms = document.querySelectorAll('.reply-form form');

    replyForms.forEach(form => {
        const textarea = form.querySelector('textarea[name="content"]');

        if (textarea) {
            // Auto-resize textarea
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 100) + 'px';
            });
        }
    });

    // Smooth scroll to new comments
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.classList.contains('comment-item')) {
                        node.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                });
            }
        });
    });

    // Observe comment lists for new additions
    const commentLists = document.querySelectorAll('[id^="comments-list-"]');
    commentLists.forEach(list => {
        observer.observe(list, { childList: true, subtree: true });
    });
});
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\components\comment-section.blade.php ENDPATH**/ ?>