<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Pending Posts</h1>
                    <p class="text-gray-600 mt-1"><?php echo e($group->name); ?> - Posts awaiting approval</p>
                </div>
                <div class="flex space-x-3">
                    <a href="<?php echo e(route('groups.show', $group)); ?>" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                        Back to Group
                    </a>
                    <a href="<?php echo e(route('groups.members', $group)); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
                        Manage Members
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-100 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Posts</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($pendingPosts->total()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Approved Posts</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($group->approvedPosts()->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-red-100 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Rejected Posts</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($group->posts()->where('approval_status', 'rejected')->count()); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Posts List -->
        <?php if($pendingPosts->count() > 0): ?>
            <div class="space-y-6">
                <?php $__currentLoopData = $pendingPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <!-- Post Header -->
                        <div class="p-4 border-b border-gray-200 bg-yellow-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img class="h-10 w-10 rounded-full" src="<?php echo e($post->user->avatar ? Storage::disk('public')->url($post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($post->user->name) . '&color=7F9CF5&background=EBF4FF'); ?>" alt="<?php echo e($post->user->name); ?>">
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900"><?php echo e($post->user->name); ?></h3>
                                        <p class="text-sm text-gray-500"><?php echo e($post->created_at->diffForHumans()); ?></p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending Approval
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <?php echo e(ucfirst($post->type)); ?>

                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Post Content -->
                        <div class="p-4">
                            <h2 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($post->title); ?></h2>

                            <!-- Post Method and Tags -->
                            <?php if($post->postMethod || $post->tags->count() > 0): ?>
                                <div class="mb-3">
                                    <div class="flex flex-wrap items-center gap-2">
                                        <?php if($post->postMethod): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                <?php echo e($post->postMethod->name); ?>

                                            </span>
                                        <?php endif; ?>
                                        <?php $__currentLoopData = $post->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white"
                                                  style="background-color: <?php echo e($tag->color); ?>">
                                                <?php echo e($tag->name); ?>

                                            </span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="text-gray-700 mb-4">
                                <?php echo nl2br(e($post->content)); ?>

                            </div>

                            <!-- Post Images -->
                            <?php if($post->images && count($post->images) > 0): ?>
                                <div class="mb-4">
                                    <?php if(count($post->images) == 1): ?>
                                        <!-- Single image - full width -->
                                        <div class="w-full">
                                            <img src="<?php echo e(Storage::disk('public')->url($post->images[0])); ?>" alt="Post image" class="rounded-lg object-cover w-full max-h-96">
                                        </div>
                                    <?php elseif(count($post->images) == 2): ?>
                                        <!-- Two images - side by side -->
                                        <div class="grid grid-cols-2 gap-2">
                                            <?php $__currentLoopData = $post->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <img src="<?php echo e(Storage::disk('public')->url($image)); ?>" alt="Post image" class="rounded-lg object-cover h-32 w-full">
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php elseif(count($post->images) == 3): ?>
                                        <!-- Three images - first full width, others side by side -->
                                        <div class="space-y-2">
                                            <img src="<?php echo e(Storage::disk('public')->url($post->images[0])); ?>" alt="Post image" class="rounded-lg object-cover w-full h-48">
                                            <div class="grid grid-cols-2 gap-2">
                                                <?php $__currentLoopData = array_slice($post->images, 1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <img src="<?php echo e(Storage::disk('public')->url($image)); ?>" alt="Post image" class="rounded-lg object-cover h-32 w-full">
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <!-- Four or more images - responsive grid -->
                                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                                            <?php $__currentLoopData = $post->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <img src="<?php echo e(Storage::disk('public')->url($image)); ?>" alt="Post image" class="rounded-lg object-cover h-32 w-full">
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endif; ?>
                                    <p class="text-sm text-gray-500 mt-2"><?php echo e(count($post->images)); ?> <?php echo e(count($post->images) == 1 ? 'image' : 'images'); ?></p>
                                </div>
                            <?php endif; ?>

                            <!-- Post Attachments -->
                            <?php if($post->fileAttachments && $post->fileAttachments->count() > 0): ?>
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-900 mb-2">Attachments:</h4>
                                    <div class="space-y-2">
                                        <?php $__currentLoopData = $post->fileAttachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="flex items-center space-x-2 p-2 bg-gray-50 rounded-md">
                                                <i class="<?php echo e(app('App\Services\FileUploadService')->getFileTypeIcon($attachment->file_type)); ?>"></i>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate"><?php echo e($attachment->original_filename); ?></p>
                                                    <p class="text-xs text-gray-500"><?php echo e($attachment->formatted_size); ?></p>
                                                </div>
                                                <a href="<?php echo e($attachment->url); ?>" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                                    Download
                                                </a>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Post Meta -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <div>
                                    <span>Posted <?php echo e($post->created_at->format('M j, Y \a\t g:i A')); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Moderation Actions -->
                        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-600">
                                    <strong>Moderation Required:</strong> This post needs your approval before it becomes visible to group members.
                                </div>
                                
                                <div class="flex items-center space-x-3">
                                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('post-approval', ['post' => $post,'group' => $group]);

$__html = app('livewire')->mount($__name, $__params, 'post-approval-'.$post->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                <?php echo e($pendingPosts->links()); ?>

            </div>
        <?php else: ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No pending posts</h3>
                <p class="mt-1 text-sm text-gray-500">
                    All posts in this group have been reviewed. New posts requiring approval will appear here.
                </p>
                <div class="mt-6">
                    <a href="<?php echo e(route('groups.show', $group)); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700">
                        Back to Group
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- Moderation Guidelines -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-4">📋 Moderation Guidelines</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800">
                <div>
                    <h4 class="font-semibold mb-2">✅ Approve posts that:</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li>Are relevant to the group's purpose</li>
                        <li>Follow community guidelines</li>
                        <li>Contribute positively to discussions</li>
                        <li>Share valuable resources or information</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-2">❌ Reject posts that:</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li>Contain spam or promotional content</li>
                        <li>Are off-topic or irrelevant</li>
                        <li>Violate community standards</li>
                        <li>Contain inappropriate content</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\groups\pending-posts.blade.php ENDPATH**/ ?>