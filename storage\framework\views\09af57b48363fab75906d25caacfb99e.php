<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">✅ Layout Reorganization Test</h1>
        <p class="text-gray-600 mb-6">This test verifies that the reorganized layout structure is working correctly.</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <h2 class="font-semibold text-green-900 mb-3">✅ Completed Tasks</h2>
                <ul class="text-green-800 space-y-2 text-sm">
                    <li>• Moved unilink-layout.blade.php to layouts folder</li>
                    <li>• Renamed unilink-sidebar-content.blade.php to unilink-left-sidebar-content.blade.php</li>
                    <li>• Updated all layout references across 33 view files</li>
                    <li>• Modified layout to extend from app.blade.php</li>
                    <li>• Created proper Blade component class</li>
                    <li>• All views compile successfully</li>
                </ul>
            </div>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h2 class="font-semibold text-blue-900 mb-3">📁 Current Structure</h2>
                <ul class="text-blue-800 space-y-1 text-sm font-mono">
                    <li>• app/View/Components/Layouts/UnilinkLayout.php</li>
                    <li>• resources/views/components/layouts/unilink-layout.blade.php</li>
                    <li>• resources/views/layouts/unilink-left-sidebar-content.blade.php</li>
                    <li>• resources/views/layouts/unilink-right-sidebar-content.blade.php</li>
                    <li>• resources/views/layouts/unilink-sidebar.blade.php (mobile)</li>
                </ul>
            </div>
        </div>
        
        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-2">Layout Features Working:</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div class="text-center">
                    <div class="w-8 h-8 bg-green-500 rounded-full mx-auto mb-1"></div>
                    <span class="text-gray-600">Left Sidebar</span>
                </div>
                <div class="text-center">
                    <div class="w-8 h-8 bg-blue-500 rounded-full mx-auto mb-1"></div>
                    <span class="text-gray-600">Main Content</span>
                </div>
                <div class="text-center">
                    <div class="w-8 h-8 bg-purple-500 rounded-full mx-auto mb-1"></div>
                    <span class="text-gray-600">Right Sidebar</span>
                </div>
                <div class="text-center">
                    <div class="w-8 h-8 bg-orange-500 rounded-full mx-auto mb-1"></div>
                    <span class="text-gray-600">Mobile Layout</span>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/test-reorganized-layout.blade.php ENDPATH**/ ?>