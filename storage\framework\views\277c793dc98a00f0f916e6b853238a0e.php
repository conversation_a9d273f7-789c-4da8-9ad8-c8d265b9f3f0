<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
        <?php if(auth()->guard()->check()): ?>
        <meta name="user-id" content="<?php echo e(auth()->id()); ?>">
        <meta name="user-role" content="<?php echo e(auth()->user()->role); ?>">
        <meta name="user-is-admin" content="<?php echo e(auth()->user()->isAdmin() ? 'true' : 'false'); ?>">
        <meta name="user-name" content="<?php echo e(auth()->user()->name); ?>">
        <meta name="user-avatar" content="<?php echo e(auth()->user()->avatar ?? ''); ?>">
        <?php endif; ?>

        <title><?php echo $__env->yieldContent('title', config('app.name', 'Laravel')); ?></title>

        <!-- Preload critical resources -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link rel="dns-prefetch" href="https://fonts.bunny.net">

        <!-- Critical CSS and JS (load first to prevent layout shifts) -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

        <!-- Fonts with display=swap to prevent layout shift -->
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Additional Styles -->
        <?php echo $__env->yieldPushContent('styles'); ?>

        <!-- Critical inline CSS to prevent flickering -->
        <style>
            /* Prevent initial layout shifts */
            .sidebar-stable {
                opacity: 1 !important;
                visibility: visible !important;
            }

            /* Hide elements until Alpine.js loads */
            [x-cloak] {
                display: none !important;
            }
        </style>

        <!-- Defer non-critical JavaScript to prevent layout blocking -->
        <script defer src="<?php echo e(asset('js/reactions.js')); ?>"></script>

        
    </head>
    <body class="font-sans antialiased <?php echo $__env->yieldContent('body-class'); ?>">
        <div class="min-h-screen <?php echo $__env->yieldContent('container-class', 'bg-gray-100'); ?>">
            <?php echo $__env->yieldContent('navigation', '@include("layouts.navigation")'); ?>

            <!-- UniLink Static Sidebars (rendered once, never re-rendered) -->
            <?php if(request()->routeIs('dashboard') || request()->routeIs('organizations.*') || request()->routeIs('groups.*') || request()->routeIs('posts.*') || request()->routeIs('profile.*') || request()->routeIs('users.*') || request()->routeIs('scholarships.*') || request()->routeIs('follow-management.*') || request()->routeIs('organization-requests.*')): ?>
                <!-- Fixed Left Sidebar (static like header) -->
                <div class="hidden lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-r border-gray-200 overflow-y-auto fixed left-0 top-16 bottom-0 z-40 sidebar-stable">
                    <?php echo $__env->make('layouts.unilink-left-sidebar-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>

                <!-- Fixed Right Sidebar (static like header) -->
                <div class="hidden lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-l border-custom-second-darkest overflow-y-auto fixed right-0 top-16 bottom-0 z-40 scrollbar-hide sidebar-stable">
                    <div class="livewire-stable">
                        <?php echo $__env->make('layouts.unilink-right-sidebar-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                </div>

                <!-- Mobile Sidebar -->
                <div class="lg:hidden">
                    <?php echo $__env->make('layouts.unilink-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>

                <!-- Mobile Sidebar Overlay -->
                <div x-data="{ open: false }"
                     x-on:toggle-sidebar.window="open = !open"
                     x-show="open"
                     x-cloak
                     x-transition:enter="transition-opacity ease-linear duration-300"
                     x-transition:enter-start="opacity-0"
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="transition-opacity ease-linear duration-300"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"
                     class="fixed inset-0 bg-custom-darkest bg-opacity-75 z-30 lg:hidden"
                     @click="$dispatch('toggle-sidebar')">
                </div>
            <?php endif; ?>

            <!-- Page Heading -->
            <?php if (! empty(trim($__env->yieldContent('header')))): ?>
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        <?php echo $__env->yieldContent('header'); ?>
                    </div>
                </header>
            <?php endif; ?>

            <!-- Page Content -->
            <?php if (! empty(trim($__env->yieldContent('content')))): ?>
                <?php echo $__env->yieldContent('content'); ?>
            <?php else: ?>
                <main>
                    <?php echo e($slot ?? ''); ?>

                </main>
            <?php endif; ?>
        </div>

        <!-- Additional Scripts -->
        <?php echo $__env->yieldPushContent('scripts'); ?>

        <!-- Ensure DOM is ready before layout-dependent operations -->
        <script>
            // Prevent layout thrashing by ensuring DOM is ready
            document.addEventListener('DOMContentLoaded', function() {
                // Force a reflow to ensure all styles are applied
                document.body.offsetHeight;

                // Initialize any layout-dependent functionality here
                if (window.initializeLayoutDependentFeatures) {
                    window.initializeLayoutDependentFeatures();
                }
            });
        </script>
    </body>
</html>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/layouts/app.blade.php ENDPATH**/ ?>