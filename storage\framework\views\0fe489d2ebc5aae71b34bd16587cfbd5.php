<?php if (isset($component)) { $__componentOriginal4969f54a92451522b65593c595a4fb0c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4969f54a92451522b65593c595a4fb0c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.unilink-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">Layout Test</h1>
        <p class="text-gray-600">This is a test page to verify the consolidated layout is working correctly.</p>
        
        <div class="mt-6 space-y-4">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h2 class="font-semibold text-blue-900">✅ Layout Features</h2>
                <ul class="mt-2 text-blue-800 space-y-1">
                    <li>• 3-column layout with left and right sidebars</li>
                    <li>• Header navigation</li>
                    <li>• Mobile responsive design</li>
                    <li>• Livewire support</li>
                    <li>• All JavaScript files loaded</li>
                </ul>
            </div>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <h2 class="font-semibold text-green-900">✅ Consolidated Features</h2>
                <ul class="mt-2 text-green-800 space-y-1">
                    <li>• Feed layout support with parameters</li>
                    <li>• Dynamic styling options</li>
                    <li>• Header slot support</li>
                    <li>• Customizable background and width</li>
                </ul>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $attributes = $__attributesOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__attributesOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $component = $__componentOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__componentOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\test-layout.blade.php ENDPATH**/ ?>