@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Color Scheme Variables */
:root {
    --color-lightest: #EEEEEE; /* Light Gray */
    --color-third-darkest: #7BC74D; /* Green */
    --color-second-darkest: #393E46; /* Dark Gray */
    --color-darkest: #222831; /* Dark Navy */
}

/* Custom Color Utility Classes */
@layer utilities {
    /* Background Colors */
    .bg-custom-lightest { background-color: var(--color-lightest); }
    .bg-custom-green { background-color: var(--color-third-darkest); }
    .bg-custom-dark-gray { background-color: var(--color-second-darkest); }
    .bg-custom-darkest { background-color: var(--color-darkest); }

    /* Text Colors */
    .text-custom-lightest { color: var(--color-lightest); }
    .text-custom-green { color: var(--color-third-darkest); }
    .text-custom-dark-gray { color: var(--color-second-darkest); }
    .text-custom-darkest { color: var(--color-darkest); }

    /* Border Colors */
    .border-custom-lightest { border-color: var(--color-lightest); }
    .border-custom-green { border-color: var(--color-third-darkest); }
    .border-custom-dark-gray { border-color: var(--color-second-darkest); }
    .border-custom-darkest { border-color: var(--color-darkest); }

    /* Hide Scrollbar */
    .scrollbar-hide {
        -ms-overflow-style: none;  /* Internet Explorer 10+ */
        scrollbar-width: none;  /* Firefox */
    }
    .scrollbar-hide::-webkit-scrollbar {
        display: none;  /* Safari and Chrome */
    }

    /* Prevent layout shifts and flickering */
    .layout-stable {
        contain: layout style;
        will-change: auto;
    }

    /* Sidebar stability during page loads */
    .sidebar-stable {
        contain: layout style;
        transform: translateZ(0); /* Force hardware acceleration */
        backface-visibility: hidden;
        perspective: 1000px;
    }

    /* Prevent Livewire component flickering */
    .livewire-stable {
        min-height: 200px; /* Reserve space to prevent layout shift */
        contain: layout;
    }

    /* Smooth transitions for layout elements */
    .layout-transition {
        transition: opacity 0.15s ease-in-out;
    }

    /* Fixed sidebar layout margins */
    .ml-70 { margin-left: 17.5rem; }
    .mr-70 { margin-right: 17.5rem; }
    .ml-80 { margin-left: 20rem; }
    .mr-80 { margin-right: 20rem; }

    /* Notification-specific background colors for subtle hover effects */
    .hover\:bg-orange-25:hover { background-color: #fef7ed; }
    .hover\:bg-blue-25:hover { background-color: #eff6ff; }
    .hover\:bg-green-25:hover { background-color: #f0fdf4; }
    .hover\:bg-purple-25:hover { background-color: #faf5ff; }
    .hover\:bg-indigo-25:hover { background-color: #eef2ff; }
    .hover\:bg-gray-25:hover { background-color: #f9fafb; }
}

/* Custom styles for UniLink */
@layer components {
    /* Feed layout responsive adjustments */
    .feed-container {
        @apply max-w-2xl mx-auto;
    }

    /* Smooth transitions for interactive elements */
    .transition-colors {
        transition-property: color, background-color, border-color;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms;
    }

    /* Auth page enhancements */
    .auth-gradient-bg {
        background: linear-gradient(135deg, #EEEEEE 0%, #f8fafc 50%, #EEEEEE 100%);
    }

    .auth-form-shadow {
        box-shadow: 0 20px 25px -5px rgba(34, 40, 49, 0.1), 0 10px 10px -5px rgba(34, 40, 49, 0.04);
    }

    .auth-input-focus {
        @apply focus:border-custom-green focus:ring-2 focus:ring-custom-green focus:ring-opacity-20;
    }

    .auth-button-hover {
        @apply hover:shadow-lg hover:scale-105 active:scale-95;
    }

    /* Custom scrollbar for sidebars */
    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f5f9;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* Line clamp utilities */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

/* 3-Column Layout Styles */
@layer utilities {
    /* Ensure proper 3-column layout behavior */
    .three-column-layout {
        display: flex;
        height: 100vh;
        padding-top: 4rem; /* Account for fixed header */
    }

    .three-column-layout > * {
        flex-shrink: 0;
    }

    .three-column-layout .central-feed {
        flex: 1;
        min-width: 0; /* Prevents flex item from overflowing */
    }

    /* Ensure body doesn't scroll when using fixed layout */
    body.fixed-layout {
        overflow: hidden;
    }

    /* Mobile responsive adjustments */
    @media (max-width: 1279px) {
        .xl\:mr-80 {
            margin-right: 0 !important;
        }

        .xl\:mr-96 {
            margin-right: 0 !important;
        }
    }

    /* Profile page specific responsive improvements */
    .profile-responsive-grid {
        display: grid;
        gap: 1.5rem;
        grid-template-columns: 1fr;
        max-width: 100%;
        overflow-x: hidden;
    }

    @media (min-width: 1024px) {
        .profile-responsive-grid {
            grid-template-columns: minmax(250px, 1fr) minmax(0, 3fr);
        }
    }

    /* Prevent horizontal overflow on all profile elements */
    .profile-container * {
        max-width: 100%;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Ensure images don't overflow */
    .profile-container img {
        max-width: 100%;
        height: auto;
    }

    /* Better text truncation */
    .text-truncate-responsive {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    @media (max-width: 640px) {
        .text-truncate-responsive {
            white-space: normal;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    }

    /* Ensure sidebars don't interfere on smaller screens */
    @media (max-width: 1023px) {
        .lg\:ml-64 {
            margin-left: 0 !important;
        }
    }
}
