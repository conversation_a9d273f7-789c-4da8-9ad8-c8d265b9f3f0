<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Group Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
        <!-- Cover Image -->
        <div class="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative">
            <?php if($group->cover_image): ?>
                <img src="<?php echo e(Storage::disk('public')->url($group->cover_image)); ?>" alt="<?php echo e($group->name); ?>" class="w-full h-full object-cover">
            <?php endif; ?>
            
            <!-- Action Buttons -->
            <div class="absolute top-4 right-4 flex space-x-2">
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('group-membership', ['group' => $group]);

$__html = app('livewire')->mount($__name, $__params, 'lw-2471564498-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

                <?php if(auth()->check() && $group->userCanModerate(auth()->user())): ?>
                    <a href="<?php echo e(route('groups.edit', $group)); ?>" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                        Edit
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Group Info -->
        <div class="p-6">
            <div class="flex items-start space-x-4">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-white rounded-lg shadow-md flex items-center justify-center -mt-13 border-4 border-white">
                        <?php if($group->logo): ?>
                            <img src="<?php echo e(Storage::disk('public')->url($group->logo)); ?>" alt="<?php echo e($group->name); ?>" class="w-16 h-16 rounded-lg object-cover">
                        <?php else: ?>
                            <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span class="text-blue-600 font-bold text-lg"><?php echo e(substr($group->name, 0, 2)); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Group Details -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <h1 class="text-2xl font-bold text-gray-900"><?php echo e($group->name); ?></h1>
                        <div class="flex space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($group->visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                <?php echo e(ucfirst($group->visibility)); ?>

                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($group->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                <?php echo e(ucfirst($group->status)); ?>

                            </span>
                        </div>
                    </div>
                    
                    <p class="text-gray-600 mt-2"><?php echo e($group->description); ?></p>
                    
                    <?php if($group->organization): ?>
                        <div class="mt-2">
                            <span class="text-sm text-gray-500">Part of </span>
                            <a href="<?php echo e(route('organizations.show', $group->organization)); ?>" class="text-sm text-blue-600 hover:text-blue-800">
                                <?php echo e($group->organization->name); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Stats -->
                    <div class="flex items-center space-x-6 mt-4 text-sm text-gray-500">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <?php echo e(is_array($group->activeMembers) ? count($group->activeMembers) : $group->activeMembers->count()); ?> members
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Created by <?php echo e($group->creator->name); ?>

                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                            </svg>
                            <?php echo e($group->created_at->format('M Y')); ?>

                        </div>
                        <?php if($group->allow_file_sharing): ?>
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                </svg>
                                File sharing enabled
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Post Creation (for members) -->
            <?php if($userMembership && $userMembership->pivot->status === 'active'): ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="flex items-center space-x-3">
                        <img class="h-10 w-10 rounded-full" src="<?php echo e(auth()->user()->avatar ? Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7F9CF5&background=EBF4FF'); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                        <button onclick="openGroupPostModal()" class="flex-1 text-left px-4 py-2 bg-gray-100 rounded-full text-gray-500 hover:bg-gray-200 transition-colors">
                            Share something with the group...
                        </button>
                        <?php if($group->allow_file_sharing): ?>
                            <button onclick="openGroupPostModal()" class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="Share files">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                </svg>
                            </button>
                        <?php endif; ?>
                        <button onclick="openGroupPostModal()" class="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors" title="Add images">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </button>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Recent Posts -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200 flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">Recent Posts</h2>
                    <?php if(auth()->check() && $group->userCanModerate(auth()->user()) && $group->post_approval === 'required'): ?>
                        <a href="<?php echo e(route('groups.pending-posts', $group)); ?>" class="text-sm text-blue-600 hover:text-blue-800">
                            Pending Posts
                        </a>
                    <?php endif; ?>
                </div>
                
                <?php if($group->approvedPosts->count() > 0): ?>
                    <div class="space-y-6">
                        <?php $__currentLoopData = $group->approvedPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if (isset($component)) { $__componentOriginal14b498b52c33a1421ff8895e4557790f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal14b498b52c33a1421ff8895e4557790f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.post-card','data' => ['post' => $post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('post-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal14b498b52c33a1421ff8895e4557790f)): ?>
<?php $attributes = $__attributesOriginal14b498b52c33a1421ff8895e4557790f; ?>
<?php unset($__attributesOriginal14b498b52c33a1421ff8895e4557790f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal14b498b52c33a1421ff8895e4557790f)): ?>
<?php $component = $__componentOriginal14b498b52c33a1421ff8895e4557790f; ?>
<?php unset($__componentOriginal14b498b52c33a1421ff8895e4557790f); ?>
<?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="p-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No posts yet</h3>
                        <p class="mt-1 text-sm text-gray-500">Be the first to share something with the group!</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Members -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Members (<?php echo e(is_array($group->activeMembers) ? count($group->activeMembers) : $group->activeMembers->count()); ?>)</h3>
                    <?php if(auth()->check() && $group->userCanModerate(auth()->user())): ?>
                        <a href="<?php echo e(route('groups.members', $group)); ?>" class="text-sm text-blue-600 hover:text-blue-800">
                            Manage
                        </a>
                    <?php endif; ?>
                </div>
                
                <div class="p-4">
                    <div class="space-y-3">
                        <?php $__currentLoopData = (is_array($group->activeMembers) ? collect($group->activeMembers) : $group->activeMembers)->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center space-x-3">
                                <img class="h-8 w-8 rounded-full" src="<?php echo e($member->avatar ? Storage::disk('public')->url($member->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($member->name) . '&color=7F9CF5&background=EBF4FF'); ?>" alt="<?php echo e($member->name); ?>">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate"><?php echo e($member->name); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo e(ucfirst($member->pivot->role)); ?></p>
                                </div>
                                <?php if($member->pivot->role === 'admin'): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                        Admin
                                    </span>
                                <?php elseif($member->pivot->role === 'moderator'): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        Moderator
                                    </span>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        
                        <?php if((is_array($group->activeMembers) ? count($group->activeMembers) : $group->activeMembers->count()) > 10): ?>
                            <div class="text-center pt-2">
                                <a href="<?php echo e(route('groups.members', $group)); ?>" class="text-sm text-blue-600 hover:text-blue-800">
                                    View all <?php echo e(is_array($group->activeMembers) ? count($group->activeMembers) : $group->activeMembers->count()); ?> members
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Group Post Creation Modal -->
    <?php if($userMembership && $userMembership->pivot->status === 'active'): ?>
        <?php if (isset($component)) { $__componentOriginalc581b6a90c114f3ae220f435636ccbff = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc581b6a90c114f3ae220f435636ccbff = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.group-post-creation-modal','data' => ['group' => $group]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('group-post-creation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['group' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($group)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc581b6a90c114f3ae220f435636ccbff)): ?>
<?php $attributes = $__attributesOriginalc581b6a90c114f3ae220f435636ccbff; ?>
<?php unset($__attributesOriginalc581b6a90c114f3ae220f435636ccbff); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc581b6a90c114f3ae220f435636ccbff)): ?>
<?php $component = $__componentOriginalc581b6a90c114f3ae220f435636ccbff; ?>
<?php unset($__componentOriginalc581b6a90c114f3ae220f435636ccbff); ?>
<?php endif; ?>
    <?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\groups\show.blade.php ENDPATH**/ ?>