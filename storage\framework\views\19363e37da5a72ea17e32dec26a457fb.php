<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center space-x-4">
                <img src="<?php echo e($user->avatar ? asset('storage/' . $user->avatar) : asset('images/default-avatar.png')); ?>" 
                     alt="<?php echo e($user->name); ?>" 
                     class="w-16 h-16 rounded-full object-cover">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900"><?php echo e($user->name); ?>'s Followers</h1>
                    <p class="text-gray-600"><?php echo e(number_format($followers->total())); ?> followers</p>
                </div>
            </div>
            
            <!-- Navigation -->
            <div class="mt-6 flex space-x-4">
                <a href="<?php echo e(route('profile.user', $user)); ?>" 
                   class="text-gray-600 hover:text-blue-600 transition-colors">
                    Profile
                </a>
                <span class="text-blue-600 font-medium">Followers</span>
                <a href="<?php echo e(route('users.following', $user)); ?>" 
                   class="text-gray-600 hover:text-blue-600 transition-colors">
                    Following
                </a>
            </div>
        </div>

        <!-- Followers List -->
        <div class="bg-white rounded-lg shadow-sm">
            <?php if($followers->count() > 0): ?>
                <div class="divide-y divide-gray-200">
                    <?php $__currentLoopData = $followers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $follower): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="p-6 flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <img src="<?php echo e($follower->avatar ? asset('storage/' . $follower->avatar) : asset('images/default-avatar.png')); ?>" 
                                     alt="<?php echo e($follower->name); ?>" 
                                     class="w-12 h-12 rounded-full object-cover">
                                <div>
                                    <h3 class="font-medium text-gray-900">
                                        <a href="<?php echo e(route('profile.user', $follower)); ?>" 
                                           class="hover:text-blue-600 transition-colors">
                                            <?php echo e($follower->name); ?>

                                        </a>
                                    </h3>
                                    <?php if($follower->bio): ?>
                                        <p class="text-sm text-gray-600 mt-1"><?php echo e(Str::limit($follower->bio, 100)); ?></p>
                                    <?php endif; ?>
                                    <p class="text-xs text-gray-500 mt-1">
                                        <?php echo e($follower->followers()->count()); ?> followers • 
                                        <?php echo e($follower->following()->count()); ?> following
                                    </p>
                                </div>
                            </div>
                            
                            <?php if(auth()->guard()->check()): ?>
                                <?php if(auth()->id() !== $follower->id): ?>
                                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('user-follower', ['user' => $follower]);

$__html = app('livewire')->mount($__name, $__params, 'follower-' . $follower->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    <?php echo e($followers->links()); ?>

                </div>
            <?php else: ?>
                <div class="p-12 text-center">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No followers yet</h3>
                    <p class="text-gray-600"><?php echo e($user->name); ?> doesn't have any followers yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\users\followers.blade.php ENDPATH**/ ?>