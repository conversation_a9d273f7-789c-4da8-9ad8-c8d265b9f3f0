<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Edit Group</h1>
                        <p class="text-gray-600 mt-1">Update your group settings and information</p>
                    </div>
                    <a href="<?php echo e(route('groups.show', $group)); ?>" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </a>
                </div>
            </div>

            <form action="<?php echo e(route('groups.update', $group)); ?>" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <!-- Basic Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Group Name *</label>
                        <input type="text" name="name" id="name" value="<?php echo e(old('name', $group->name)); ?>" required
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               placeholder="Enter group name">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea name="description" id="description" rows="3"
                                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                  placeholder="Describe what this group is about..."><?php echo e(old('description', $group->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="organization_id" class="block text-sm font-medium text-gray-700 mb-1">Associated Organization (Optional)</label>
                        <select name="organization_id" id="organization_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">No organization</option>
                            <?php $__currentLoopData = $organizations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $org): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($org->id); ?>" <?php echo e(old('organization_id', $group->organization_id) == $org->id ? 'selected' : ''); ?>>
                                    <?php echo e($org->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['organization_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Privacy Settings -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">Privacy & Access</h3>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Group Visibility *</label>
                        <div class="space-y-2">
                            <label class="flex items-start space-x-3">
                                <input type="radio" name="visibility" value="public" <?php echo e(old('visibility', $group->visibility) === 'public' ? 'checked' : ''); ?>

                                       class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Public</div>
                                    <div class="text-sm text-gray-500">Anyone can see the group and join immediately</div>
                                </div>
                            </label>
                            <label class="flex items-start space-x-3">
                                <input type="radio" name="visibility" value="private" <?php echo e(old('visibility', $group->visibility) === 'private' ? 'checked' : ''); ?>

                                       class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Private</div>
                                    <div class="text-sm text-gray-500">Only members can see the group, join requests need approval</div>
                                </div>
                            </label>
                        </div>
                        <?php $__errorArgs = ['visibility'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Post Approval *</label>
                        <div class="space-y-2">
                            <label class="flex items-start space-x-3">
                                <input type="radio" name="post_approval" value="none" <?php echo e(old('post_approval', $group->post_approval) === 'none' ? 'checked' : ''); ?>

                                       class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">No Approval Required</div>
                                    <div class="text-sm text-gray-500">Members can post immediately</div>
                                </div>
                            </label>
                            <label class="flex items-start space-x-3">
                                <input type="radio" name="post_approval" value="required" <?php echo e(old('post_approval', $group->post_approval) === 'required' ? 'checked' : ''); ?>

                                       class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Approval Required</div>
                                    <div class="text-sm text-gray-500">Posts need moderator approval before being visible</div>
                                </div>
                            </label>
                        </div>
                        <?php $__errorArgs = ['post_approval'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- File Sharing Settings -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">File Sharing</h3>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="allow_file_sharing" id="allow_file_sharing" value="1" 
                               <?php echo e(old('allow_file_sharing', $group->allow_file_sharing) ? 'checked' : ''); ?>

                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="allow_file_sharing" class="ml-2 text-sm text-gray-900">
                            Allow members to share files
                        </label>
                    </div>

                    <div id="file-sharing-options" class="space-y-4 <?php echo e(old('allow_file_sharing', $group->allow_file_sharing) ? '' : 'hidden'); ?>">
                        <div>
                            <label for="max_file_size_mb" class="block text-sm font-medium text-gray-700 mb-1">Maximum File Size (MB)</label>
                            <input type="number" name="max_file_size_mb" id="max_file_size_mb" 
                                   value="<?php echo e(old('max_file_size_mb', $group->max_file_size_mb)); ?>" min="1" max="100"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <?php $__errorArgs = ['max_file_size_mb'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Allowed File Types</label>
                            <div class="grid grid-cols-2 gap-2">
                                <?php
                                    $fileTypes = [
                                        'pdf' => 'PDF Documents',
                                        'doc' => 'Word Documents',
                                        'docx' => 'Word Documents (New)',
                                        'txt' => 'Text Files',
                                        'jpg' => 'JPEG Images',
                                        'png' => 'PNG Images',
                                        'gif' => 'GIF Images',
                                        'zip' => 'ZIP Archives'
                                    ];
                                    $currentTypes = old('allowed_file_types', $group->allowed_file_types ?? []);
                                ?>
                                <?php $__currentLoopData = $fileTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" name="allowed_file_types[]" value="<?php echo e($type); ?>"
                                               <?php echo e(in_array($type, $currentTypes) ? 'checked' : ''); ?>

                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="text-sm text-gray-700"><?php echo e($label); ?></span>
                                    </label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php $__errorArgs = ['allowed_file_types'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Current Images -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">Group Images</h3>
                    
                    <!-- Current Logo -->
                    <?php if($group->logo): ?>
                        <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                            <img src="<?php echo e(Storage::disk('public')->url($group->logo)); ?>" alt="Current logo" class="w-16 h-16 rounded-lg object-cover">
                            <div>
                                <p class="text-sm font-medium text-gray-900">Current Logo</p>
                                <p class="text-sm text-gray-500">Upload a new logo to replace this one</p>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div>
                        <label for="logo" class="block text-sm font-medium text-gray-700 mb-1">Group Logo</label>
                        <input type="file" name="logo" id="logo" accept="image/*"
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <p class="mt-1 text-sm text-gray-500">Recommended size: 200x200px. Max size: 2MB.</p>
                        <?php $__errorArgs = ['logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Current Cover -->
                    <?php if($group->cover_image): ?>
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <img src="<?php echo e(Storage::disk('public')->url($group->cover_image)); ?>" alt="Current cover" class="w-full h-32 rounded-lg object-cover">
                            <p class="text-sm font-medium text-gray-900 mt-2">Current Cover Image</p>
                            <p class="text-sm text-gray-500">Upload a new cover image to replace this one</p>
                        </div>
                    <?php endif; ?>

                    <div>
                        <label for="cover_image" class="block text-sm font-medium text-gray-700 mb-1">Cover Image</label>
                        <input type="file" name="cover_image" id="cover_image" accept="image/*"
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <p class="mt-1 text-sm text-gray-500">Recommended size: 1200x400px. Max size: 5MB.</p>
                        <?php $__errorArgs = ['cover_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div class="flex space-x-3">
                        <a href="<?php echo e(route('groups.show', $group)); ?>" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400">
                            Cancel
                        </a>
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700">
                            Update Group
                        </button>
                    </div>
                    
                    <!-- Danger Zone -->
                    <?php if($group->created_by === auth()->id() || auth()->user()->isAdmin()): ?>
                        <button type="button" onclick="confirmDelete()" class="bg-red-600 text-white px-4 py-2 rounded-md font-medium hover:bg-red-700">
                            Delete Group
                        </button>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Group</h3>
            <p class="text-sm text-gray-500 mb-6">
                Are you sure you want to delete this group? This action cannot be undone and will remove all posts, members, and files associated with the group.
            </p>
            <div class="flex justify-end space-x-3">
                <button onclick="closeDeleteModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400">
                    Cancel
                </button>
                <form action="<?php echo e(route('groups.destroy', $group)); ?>" method="POST" class="inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-md font-medium hover:bg-red-700">
                        Delete Group
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Toggle file sharing options
        document.getElementById('allow_file_sharing').addEventListener('change', function() {
            const options = document.getElementById('file-sharing-options');
            if (this.checked) {
                options.classList.remove('hidden');
            } else {
                options.classList.add('hidden');
            }
        });

        // Delete confirmation
        function confirmDelete() {
            document.getElementById('deleteModal').classList.remove('hidden');
            document.getElementById('deleteModal').classList.add('flex');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.add('hidden');
            document.getElementById('deleteModal').classList.remove('flex');
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\groups\edit.blade.php ENDPATH**/ ?>