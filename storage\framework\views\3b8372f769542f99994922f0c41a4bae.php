<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Organization Requests</h1>
                <p class="text-gray-600 mt-1">Track your organization creation requests</p>
            </div>
            <a href="<?php echo e(route('organization-requests.create')); ?>" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                New Request
            </a>
        </div>
    </div>

    <?php if($requests->count() > 0): ?>
        <div class="space-y-4">
            <?php $__currentLoopData = $requests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $request): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-gray-900">
                                    <a href="<?php echo e(route('organization-requests.show', $request)); ?>" class="hover:text-blue-600">
                                        <?php echo e($request->organization_name); ?>

                                    </a>
                                </h3>
                                
                                <!-- Status Badge -->
                                <?php if($request->isPending()): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending
                                    </span>
                                <?php elseif($request->isApproved()): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Approved
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Rejected
                                    </span>
                                <?php endif; ?>

                                <?php if($request->hasCreatedOrganization()): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Organization Created
                                    </span>
                                <?php endif; ?>
                            </div>

                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                <?php echo e(Str::limit($request->description, 150)); ?>

                            </p>

                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span>Submitted <?php echo e($request->created_at->diffForHumans()); ?></span>
                                
                                <?php if($request->reviewed_at): ?>
                                    <span>•</span>
                                    <span>Reviewed <?php echo e($request->reviewed_at->diffForHumans()); ?></span>
                                <?php endif; ?>

                                <?php if($request->reviewer): ?>
                                    <span>•</span>
                                    <span>by <?php echo e($request->reviewer->name); ?></span>
                                <?php endif; ?>
                            </div>

                            <?php if($request->admin_notes): ?>
                                <div class="mt-3 p-3 bg-gray-50 rounded-lg">
                                    <p class="text-sm text-gray-700">
                                        <span class="font-medium">Admin Notes:</span>
                                        <?php echo e(Str::limit($request->admin_notes, 100)); ?>

                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="flex items-center space-x-2 ml-4">
                            <?php if($request->isApproved() && !$request->hasCreatedOrganization()): ?>
                                <a href="<?php echo e(route('organizations.create')); ?>" 
                                   class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    Create Organization
                                </a>
                            <?php endif; ?>

                            <?php if($request->hasCreatedOrganization()): ?>
                                <a href="<?php echo e(route('organizations.show', $request->createdOrganization)); ?>" 
                                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    View Organization
                                </a>
                            <?php endif; ?>

                            <a href="<?php echo e(route('organization-requests.show', $request)); ?>" 
                               class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <?php if($requests->hasPages()): ?>
            <div class="mt-8">
                <?php echo e($requests->links()); ?>

            </div>
        <?php endif; ?>
    <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No organization requests</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by submitting your first organization request.</p>
            <div class="mt-6">
                <a href="<?php echo e(route('organization-requests.create')); ?>" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Submit Request
                </a>
            </div>
        </div>
    <?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\organization-requests\index.blade.php ENDPATH**/ ?>