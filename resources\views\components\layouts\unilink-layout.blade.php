@extends('layouts.app')

@section('title', config('app.name', 'UniLink'))

@push('styles')
    @livewireStyles
@endpush

@push('scripts')
    <!-- Defer non-critical JavaScript to prevent layout blocking -->
    <script defer src="{{ asset('js/post-summary-updater.js') }}"></script>
    <script defer src="{{ asset('js/comments.js') }}"></script>
    <script defer src="{{ asset('js/comment-modal.js') }}"></script>
    @livewireScripts
@endpush

@section('body-class', ($bodyClass ?? 'bg-gray-100') . ' overflow-hidden')

@section('navigation')
    @include('layouts.unilink-header')
@endsection

@section('content')
    <!-- Main 3-Column Container -->
    <div class="flex h-screen pt-16 layout-stable">
        <!-- Left Sidebar -->
        <div class="hidden lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-r {{ $leftBorderClass ?? 'border-gray-200' }} overflow-y-auto flex-shrink-0 sidebar-stable">
            @include('layouts.unilink-left-sidebar-content')
        </div>

        <!-- Central Content -->
        <main class="flex-1 {{ $mainBgClass ?? 'bg-gray-100' }} overflow-y-auto min-w-0 scrollbar-hide">
            <div class="{{ $maxWidthClass ?? 'max-w-6xl' }} mx-auto px-4 {{ $paddingClass ?? 'py-6' }}">
                <!-- Header (if provided) -->
                @isset($header)
                    <div class="mb-6">
                        {{ $header }}
                    </div>
                @endisset

                <!-- Main Content -->
                @if(isset($feedLayout) && $feedLayout)
                    <div class="space-y-4">
                        {{ $slot }}
                    </div>
                @else
                    {{ $slot }}
                @endif
            </div>
        </main>

        <!-- Right Sidebar -->
        <div class="hidden lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-l border-custom-second-darkest overflow-y-auto flex-shrink-0 scrollbar-hide sidebar-stable">
            @include('layouts.unilink-right-sidebar-content')
        </div>
    </div>

    <!-- Mobile Sidebar -->
    <div class="lg:hidden">
        @include('layouts.unilink-sidebar')
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div x-data="{ open: false }"
         x-on:toggle-sidebar.window="open = !open"
         x-show="open"
         x-cloak
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 {{ $overlayClass ?? 'bg-custom-darkest' }} bg-opacity-75 z-30 lg:hidden"
         @click="$dispatch('toggle-sidebar')">
    </div>

    <!-- Notification Popup -->
    @include('components.notification-popup')

    <!-- Image Modal -->
    @include('components.image-modal')
@endsection