@extends('layouts.app')

@section('title', config('app.name', 'UniLink'))

@push('styles')
    @livewireStyles
@endpush

@push('scripts')
    <!-- Defer non-critical JavaScript to prevent layout blocking -->
    <script defer src="{{ asset('js/post-summary-updater.js') }}"></script>
    <script defer src="{{ asset('js/comments.js') }}"></script>
    <script defer src="{{ asset('js/comment-modal.js') }}"></script>
    @livewireScripts
@endpush

@section('body-class', ($bodyClass ?? 'bg-gray-100') . ' overflow-hidden')

@section('navigation')
    @include('layouts.unilink-header')
@endsection

@section('content')
    <!-- Central Content with proper margins for fixed sidebars -->
    <main class="lg:ml-70 xl:ml-80 lg:mr-70 xl:mr-80 {{ $mainBgClass ?? 'bg-gray-100' }} pt-16">
        <div class="central-content-scroll scrollbar-hide">
            <div class="{{ $maxWidthClass ?? 'max-w-6xl' }} mx-auto px-4 {{ $paddingClass ?? 'py-6' }}">
                <!-- Header (if provided) -->
                @isset($header)
                    <div class="mb-6">
                        {{ $header }}
                    </div>
                @endisset

                <!-- Main Content -->
                @if(isset($feedLayout) && $feedLayout)
                    <div class="space-y-4">
                        {{ $slot }}
                    </div>
                @else
                    {{ $slot }}
                @endif
            </div>
        </div>
    </main>

    <!-- Notification Popup -->
    @include('components.notification-popup')

    <!-- Image Modal -->
    @include('components.image-modal')
@endsection