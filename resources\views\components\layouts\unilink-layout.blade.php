@extends('layouts.app')

@section('title', config('app.name', 'UniLink'))

@push('styles')
    @livewireStyles
@endpush

@push('scripts')
    <!-- Defer non-critical JavaScript to prevent layout blocking -->
    <script defer src="{{ asset('js/post-summary-updater.js') }}"></script>
    <script defer src="{{ asset('js/comments.js') }}"></script>
    <script defer src="{{ asset('js/comment-modal.js') }}"></script>
    @livewireScripts
@endpush

@section('body-class', ($bodyClass ?? 'bg-gray-100') . ' overflow-hidden')

@section('navigation')
    @include('layouts.unilink-header')
@endsection

@section('content')
    <!-- Fixed Left Sidebar with anti-flicker optimization -->
    <div id="left-sidebar" class="hidden lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-r {{ $leftBorderClass ?? 'border-gray-200' }} overflow-y-auto fixed left-0 top-16 bottom-0 z-40 sidebar-stable" style="opacity: 1; visibility: visible;">
        @include('layouts.unilink-left-sidebar-content')
    </div>

    <!-- Fixed Right Sidebar with anti-flicker optimization -->
    <div id="right-sidebar" class="hidden lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-l border-custom-second-darkest overflow-y-auto fixed right-0 top-16 bottom-0 z-40 scrollbar-hide sidebar-stable" style="opacity: 1; visibility: visible;">
        <div class="livewire-stable">
            @include('layouts.unilink-right-sidebar-content')
        </div>
    </div>

    <!-- Central Content with proper margins for fixed sidebars -->
    <main class="lg:ml-70 xl:ml-80 lg:mr-70 xl:mr-80 {{ $mainBgClass ?? 'bg-gray-100' }} min-h-screen pt-16 overflow-y-auto">
        <div class="{{ $maxWidthClass ?? 'max-w-6xl' }} mx-auto px-4 {{ $paddingClass ?? 'py-6' }}">
            <!-- Header (if provided) -->
            @isset($header)
                <div class="mb-6">
                    {{ $header }}
                </div>
            @endisset

            <!-- Main Content -->
            @if(isset($feedLayout) && $feedLayout)
                <div class="space-y-4">
                    {{ $slot }}
                </div>
            @else
                {{ $slot }}
            @endif
        </div>
    </main>

    <!-- Mobile Sidebar -->
    <div class="lg:hidden">
        @include('layouts.unilink-sidebar')
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div x-data="{ open: false }"
         x-on:toggle-sidebar.window="open = !open"
         x-show="open"
         x-cloak
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 {{ $overlayClass ?? 'bg-custom-darkest' }} bg-opacity-75 z-30 lg:hidden"
         @click="$dispatch('toggle-sidebar')">
    </div>

    <!-- Notification Popup -->
    @include('components.notification-popup')

    <!-- Image Modal -->
    @include('components.image-modal')

    <!-- Anti-flicker JavaScript -->
    <script>
        // Prevent sidebar flickering during navigation
        document.addEventListener('DOMContentLoaded', function() {
            // Cache sidebar content to prevent re-rendering flicker
            const leftSidebar = document.getElementById('left-sidebar');
            const rightSidebar = document.getElementById('right-sidebar');

            if (leftSidebar && rightSidebar) {
                // Force immediate visibility
                leftSidebar.style.opacity = '1';
                leftSidebar.style.visibility = 'visible';
                rightSidebar.style.opacity = '1';
                rightSidebar.style.visibility = 'visible';

                // Store sidebar content in sessionStorage to prevent re-renders
                const leftContent = leftSidebar.innerHTML;
                const rightContent = rightSidebar.innerHTML;

                // Mark sidebars as loaded to prevent future flickers
                leftSidebar.setAttribute('data-loaded', 'true');
                rightSidebar.setAttribute('data-loaded', 'true');

                // Prevent Livewire from re-hydrating sidebar components unnecessarily
                if (window.Livewire) {
                    // Disable automatic re-hydration for sidebar components
                    const livewireComponents = rightSidebar.querySelectorAll('[wire\\:id]');
                    livewireComponents.forEach(component => {
                        component.setAttribute('wire:ignore', '');
                    });
                }
            }
        });

        // Prevent flicker on page transitions
        window.addEventListener('beforeunload', function() {
            const leftSidebar = document.getElementById('left-sidebar');
            const rightSidebar = document.getElementById('right-sidebar');

            if (leftSidebar && rightSidebar) {
                // Keep sidebars visible during transition
                leftSidebar.style.opacity = '1';
                rightSidebar.style.opacity = '1';
            }
        });
    </script>
@endsection