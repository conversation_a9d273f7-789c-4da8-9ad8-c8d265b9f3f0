<x-layouts.unilink-layout
    :bodyClass="'bg-custom-lightest'"
    :mainBgClass="'bg-custom-lightest'"
    :maxWidthClass="'max-w-3xl'"
    :leftBorderClass="'border-custom-second-darkest'"
    :overlayClass="'bg-custom-darkest'"
    :feedLayout="true">

    <!-- Create Post Card -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-4 mb-4">
        <div class="flex items-center space-x-3">
            <img class="h-10 w-10 rounded-full" src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
            <div class="flex-1">
                <button onclick="openPersonalPostModal()" class="w-full text-left px-4 py-3 bg-custom-lightest hover:bg-custom-lightest hover:bg-opacity-80 rounded-full text-custom-second-darkest transition-colors">
                    What's on your mind, {{ auth()->user()->name }}?
                </button>
            </div>
        </div>
        <div class="flex items-center justify-between mt-3 pt-3 border-t border-custom-second-darkest border-opacity-20">
            <button onclick="openPersonalPostModal()" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm font-medium">Photo/Video</span>
            </button>
            <button onclick="openPersonalPostModal()" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm font-medium">Event</span>
            </button>
            <a href="{{ route('scholarships.create') }}" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" />
                </svg>
                <span class="text-sm font-medium">Scholarship</span>
            </a>
        </div>
    </div>

    <!-- Filter Pills -->
    <!-- <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-3 mb-4">
        <div class="flex flex-wrap gap-2">
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-green rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                All Posts
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Events
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Scholarships
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Announcements
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Organizations
            </button>
        </div>
    </div> -->

    <!-- Compact Filter Bar -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 mb-6">
        <!-- Search Box and Filter Toggle -->
        <div class="p-4 pb-3">
            <div class="flex gap-3 items-center">
                <!-- Search Box -->
                <div class="flex-1 relative">
                    <input type="text"
                           id="post-search"
                           placeholder="Search posts... [event, tuition, org name]"
                           class="w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-lg focus:ring-custom-green focus:border-custom-green text-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    <button id="search-clear"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 hidden">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Filter Toggle Button -->
                <button onclick="toggleFilters()" id="filter-toggle-btn"
                        class="px-4 py-2.5 bg-gray-50 hover:bg-gray-100 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 transition-colors flex items-center gap-2">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
                    </svg>
                    <span id="filter-toggle-text">Filters</span>
                    <svg id="filter-toggle-icon" class="h-4 w-4 text-gray-500 transition-transform duration-200" fill="none" viewBox="0 0 20 20" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2 7l8 8 8-8" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Collapsible Filter Section -->
        <div id="filters-section" class="hidden border-t border-gray-200 p-4 pt-3 space-y-4">
            <!-- Post Type/Source Filter -->
            <div class="flex flex-wrap items-center gap-3">
                <span class="text-sm font-medium text-gray-700 min-w-fit">📝 Post Type:</span>
                <div class="flex flex-wrap gap-2">
                    <button onclick="setPostTypeFilter('all')" id="filter-all"
                            class="filter-btn px-3 py-1.5 rounded-full text-sm font-medium border transition-colors bg-custom-green text-white border-custom-green">
                        🔘 All
                    </button>
                    <button onclick="setPostTypeFilter('user')" id="filter-user"
                            class="filter-btn px-3 py-1.5 rounded-full text-sm font-medium border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50">
                        👤 User Posts
                    </button>
                    <button onclick="setPostTypeFilter('organization')" id="filter-organization"
                            class="filter-btn px-3 py-1.5 rounded-full text-sm font-medium border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50">
                        🏛️ Org Posts
                    </button>
                    <button onclick="setPostTypeFilter('group')" id="filter-group"
                            class="filter-btn px-3 py-1.5 rounded-full text-sm font-medium border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50">
                        👥 Group Posts
                    </button>
                </div>
            </div>

            <!-- Tags Filter -->
            <div class="flex flex-wrap items-center gap-3">
                <span class="text-sm font-medium text-gray-700 min-w-fit">🏷️ Tags:</span>
                <div class="flex-1">
                    <button onclick="toggleTagFilters()" id="tag-filter-toggle"
                            class="px-3 py-1.5 rounded-lg text-sm font-medium border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors flex items-center">
                        <span id="tag-toggle-text">Select Tags</span>
                        <svg id="tag-toggle-icon" class="inline-block ml-1 h-4 w-4 text-gray-500 transition-transform duration-200" fill="none" viewBox="0 0 20 20" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2 7l8 8 8-8" />
                        </svg>
                    </button>

                    <!-- Selected Tags Display -->
                    <div id="selected-tags" class="hidden flex flex-wrap gap-2 mt-3 p-2 bg-blue-50 rounded-lg border border-blue-200"></div>

                    <!-- Tag Selection Grid -->
                    <div id="tag-filters-container" class="hidden p-4 bg-gray-50 rounded-lg mt-2 border border-gray-200">
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
                            <!-- Tags will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Date and Visibility Filters Row -->
            <div class="flex flex-wrap items-center gap-6">
                <!-- Date Filter -->
                <div class="flex items-center gap-3">
                    <span class="text-sm font-medium text-gray-700 min-w-fit">🗓️ Date:</span>
                    <div class="relative">
                        <select id="date-filter" onchange="applyFilters()"
                                class="appearance-none bg-white border border-gray-300 rounded-lg px-3 py-1.5 pr-8 text-sm focus:ring-custom-green focus:border-custom-green">
                            <option value="all">All Time</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                        </div>
                    </div>
                </div>

                <!-- Visibility Filter -->
                <div id="visibility-filter-section" class="flex items-center gap-3">
                    <span class="text-sm font-medium text-gray-700 min-w-fit">👁️ Visibility:</span>
                    <div class="flex flex-wrap gap-2">
                        <button onclick="setVisibilityFilter('all')" id="visibility-all"
                                class="visibility-btn px-2.5 py-1 rounded-full text-xs font-medium border transition-colors bg-custom-green text-white border-custom-green">
                            🔘 All
                        </button>
                        <button onclick="setVisibilityFilter('public')" id="visibility-public"
                                class="visibility-btn px-2.5 py-1 rounded-full text-xs font-medium border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50">
                            🌐 Public
                        </button>
                        <button onclick="setVisibilityFilter('followers_only')" id="visibility-followers_only"
                                class="visibility-btn px-2.5 py-1 rounded-full text-xs font-medium border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50">
                            🤝 Followers
                        </button>
                        <button onclick="setVisibilityFilter('university_only')" id="visibility-university_only"
                                class="visibility-btn px-2.5 py-1 rounded-full text-xs font-medium border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50">
                            🏫 University
                        </button>
                        <button onclick="setVisibilityFilter('organization_only')" id="visibility-organization_only"
                                class="visibility-btn px-2.5 py-1 rounded-full text-xs font-medium border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50">
                            🏛️ Org Only
                        </button>
                        <button onclick="setVisibilityFilter('group_members_only')" id="visibility-group_members_only"
                                class="visibility-btn px-2.5 py-1 rounded-full text-xs font-medium border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50">
                            👥 Group Only
                        </button>
                    </div>
                </div>
            </div>

            <!-- Clear Filters -->
            <div class="flex justify-end">
                <button onclick="clearAllFilters()"
                        class="text-sm text-gray-500 hover:text-gray-700 transition-colors">
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Loading State -->
        <div id="filter-loading" class="hidden mt-4 text-center">
            <div class="flex items-center justify-center space-x-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-custom-green"></div>
                <span class="text-sm text-gray-600">Filtering posts...</span>
            </div>
        </div>
    </div>

    <!-- Main Feed -->
    <div class="space-y-6" id="posts-container">
        @forelse($posts as $feedItem)
            @if($feedItem->type === 'post')
                <x-post-card :post="$feedItem->data" />
            @elseif($feedItem->type === 'share')
                <x-shared-post-card :share="$feedItem->data" />
            @endif
        @empty
            <!-- Empty State -->
            <div id="empty-state" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10m0 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v2m10 0v10a2 2 0 01-2 2H9a2 2 0 01-2-2V8m10 0H7m0 0V6a2 2 0 012-2h10a2 2 0 012 2v2M7 8v10a2 2 0 002 2h10a2 2 0 002-2V8" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No posts yet</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your first post!</p>
                <div class="mt-6">
                    <button onclick="openPersonalPostModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-custom-green hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Create Post
                    </button>
                </div>
            </div>
        @endforelse

        <!-- Infinite Scroll Loading Indicator -->
        <div id="infinite-scroll-loading" class="hidden flex justify-center py-6">
            <div class="flex items-center space-x-2 text-gray-500">
                <svg class="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Loading more posts...</span>
            </div>
        </div>

        <!-- End of Feed Indicator -->
        <div id="end-of-feed" class="hidden text-center py-8 text-gray-500">
            <p>You've reached the end of your feed!</p>
        </div>
    </div>

    <!-- Personal Post Creation Modal -->
    @include('components.personal-post-creation-modal')

    <!-- Simple Filter JavaScript -->
    <script>
        let currentFilters = {
            search: '',
            postType: 'all',
            dateRange: 'all',
            visibility: 'all',
            tags: []
        };

        let availableTags = [];
        let tagsLoaded = false;
        let filtersVisible = false;

        // Infinite scroll variables
        let isLoading = false;
        let hasMorePosts = true;
        let nextCursor = null;

        // Initialize search functionality and infinite scroll
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('post-search');
            const searchClear = document.getElementById('search-clear');
            let searchTimeout;

            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    // Show/hide clear button
                    if (this.value.length > 0) {
                        searchClear.classList.remove('hidden');
                    } else {
                        searchClear.classList.add('hidden');
                    }

                    // Debounced search
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        currentFilters.search = this.value;
                        applyFilters();
                    }, 500);
                });

                searchClear.addEventListener('click', function() {
                    searchInput.value = '';
                    searchClear.classList.add('hidden');
                    currentFilters.search = '';
                    applyFilters();
                });
            }

            // Initialize infinite scroll
            initializeInfiniteScroll();
        });

        // Initialize infinite scroll functionality
        function initializeInfiniteScroll() {
            // Get initial cursor from the last post
            const posts = document.querySelectorAll('#posts-container > div');
            if (posts.length > 0) {
                // Extract timestamp from the last post (you may need to adjust this based on your post structure)
                const lastPost = posts[posts.length - 1];
                const timeElement = lastPost.querySelector('[data-timestamp]');
                if (timeElement) {
                    nextCursor = timeElement.getAttribute('data-timestamp');
                }
            }

            // Add scroll event listener
            window.addEventListener('scroll', handleScroll);
        }

        // Handle scroll events for infinite loading
        function handleScroll() {
            if (isLoading || !hasMorePosts) return;

            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // Load more when user is 200px from bottom
            if (scrollTop + windowHeight >= documentHeight - 200) {
                loadMorePosts();
            }
        }

        // Toggle filters visibility
        function toggleFilters() {
            const filtersSection = document.getElementById('filters-section');
            const toggleIcon = document.getElementById('filter-toggle-icon');
            const toggleText = document.getElementById('filter-toggle-text');

            filtersVisible = !filtersVisible;

            if (filtersVisible) {
                filtersSection.classList.remove('hidden');
                toggleIcon.classList.add('rotate-180');
                toggleText.textContent = 'Hide Filters';
            } else {
                filtersSection.classList.add('hidden');
                toggleIcon.classList.remove('rotate-180');
                toggleText.textContent = 'Filters';
            }
        }

        function setPostTypeFilter(type) {
            currentFilters.postType = type;

            // Update button states
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('bg-custom-green', 'text-white', 'border-custom-green');
                btn.classList.add('bg-white', 'text-gray-700', 'border-gray-300');
            });

            const activeBtn = document.getElementById('filter-' + type);
            if (activeBtn) {
                activeBtn.classList.remove('bg-white', 'text-gray-700', 'border-gray-300');
                activeBtn.classList.add('bg-custom-green', 'text-white', 'border-custom-green');
            }

            // Update visibility filter options based on post type
            updateVisibilityFilterOptions(type);

            // Update available tags based on post type
            updateAvailableTags();

            applyFilters();
        }

        function updateVisibilityFilterOptions(postType) {
            const visibilitySection = document.getElementById('visibility-filter-section');
            const followersBtn = document.getElementById('visibility-followers_only');
            const universityBtn = document.getElementById('visibility-university_only');
            const organizationBtn = document.getElementById('visibility-organization_only');
            const groupBtn = document.getElementById('visibility-group_members_only');

            // Reset visibility filter to 'all' when changing post type
            currentFilters.visibility = 'all';
            setVisibilityFilter('all');

            // Show/hide visibility options based on post type
            if (postType === 'user') {
                // For user posts, show followers and university, hide org and group
                if (followersBtn) followersBtn.style.display = 'inline-block';
                if (universityBtn) universityBtn.style.display = 'inline-block';
                if (organizationBtn) organizationBtn.style.display = 'none';
                if (groupBtn) groupBtn.style.display = 'none';
            } else if (postType === 'organization') {
                // For org posts, show university and organization, hide followers and group
                if (followersBtn) followersBtn.style.display = 'none';
                if (universityBtn) universityBtn.style.display = 'inline-block';
                if (organizationBtn) organizationBtn.style.display = 'inline-block';
                if (groupBtn) groupBtn.style.display = 'none';
            } else if (postType === 'group') {
                // For group posts, show university and group, hide followers and organization
                if (followersBtn) followersBtn.style.display = 'none';
                if (universityBtn) universityBtn.style.display = 'inline-block';
                if (organizationBtn) organizationBtn.style.display = 'none';
                if (groupBtn) groupBtn.style.display = 'inline-block';
            } else {
                // For 'all', show all options
                if (followersBtn) followersBtn.style.display = 'inline-block';
                if (universityBtn) universityBtn.style.display = 'inline-block';
                if (organizationBtn) organizationBtn.style.display = 'inline-block';
                if (groupBtn) groupBtn.style.display = 'inline-block';
            }
        }

        function setVisibilityFilter(visibility) {
            currentFilters.visibility = visibility;

            // Update button states
            document.querySelectorAll('.visibility-btn').forEach(btn => {
                btn.classList.remove('bg-custom-green', 'text-white', 'border-custom-green');
                btn.classList.add('bg-white', 'text-gray-700', 'border-gray-300');
            });

            const activeBtn = document.getElementById('visibility-' + visibility);
            if (activeBtn) {
                activeBtn.classList.remove('bg-white', 'text-gray-700', 'border-gray-300');
                activeBtn.classList.add('bg-custom-green', 'text-white', 'border-custom-green');
            }

            applyFilters();
        }

        // Tag filtering functions
        async function toggleTagFilters() {
            const container = document.getElementById('tag-filters-container');
            const toggle = document.getElementById('tag-filter-toggle');
            const toggleText = document.getElementById('tag-toggle-text');
            const toggleIcon = document.getElementById('tag-toggle-icon');

            if (container.classList.contains('hidden')) {
                // Load tags if not already loaded
                if (!tagsLoaded) {
                    await loadAllTags();
                }
                container.classList.remove('hidden');
                toggleText.textContent = 'Hide Tags';
                toggleIcon.classList.add('rotate-180');
            } else {
                container.classList.add('hidden');
                toggleText.textContent = 'Show Tags';
                toggleIcon.classList.remove('rotate-180');
            }
        }

        async function loadAllTags() {
            try {
                const response = await fetch('/api/tags', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    availableTags = data.tags || [];
                    renderTagFilters();
                    tagsLoaded = true;
                } else {
                    // Fallback to hardcoded tags if API fails
                    availableTags = @json(\App\Models\Tag::with('postMethod')->active()->get());
                    renderTagFilters();
                    tagsLoaded = true;
                }
            } catch (error) {
                console.error('Error loading tags:', error);
                // Fallback to hardcoded tags
                availableTags = @json(\App\Models\Tag::with('postMethod')->active()->get());
                renderTagFilters();
                tagsLoaded = true;
            }
        }

        function renderTagFilters() {
            const container = document.getElementById('tag-filters-container');
            container.innerHTML = '';

            // Filter tags based on current post type
            let filteredTags = availableTags;
            if (currentFilters.postType !== 'all') {
                const postMethodMap = {
                    'user': 'user',
                    'organization': 'organization',
                    'group': 'group'
                };
                const methodSlug = postMethodMap[currentFilters.postType];
                if (methodSlug) {
                    filteredTags = availableTags.filter(tag =>
                        tag.post_method && tag.post_method.slug === methodSlug
                    );
                }
            }

            const gridContainer = container.querySelector('.grid') || container;

            filteredTags.forEach(tag => {
                const tagDiv = document.createElement('div');
                tagDiv.className = 'flex items-center p-2 bg-white rounded-md border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all duration-200';
                tagDiv.innerHTML = `
                    <input type="checkbox" id="tag_${tag.id}"
                           onchange="toggleTag(${tag.id})"
                           ${currentFilters.tags.includes(tag.id) ? 'checked' : ''}
                           class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded flex-shrink-0">
                    <label for="tag_${tag.id}" class="ml-2 text-sm text-gray-700 flex items-center cursor-pointer min-w-0 flex-1">
                        <span class="inline-block w-3 h-3 rounded-full mr-2 flex-shrink-0" style="background-color: ${tag.color || '#3B82F6'}"></span>
                        <span class="truncate">${tag.name}</span>
                    </label>
                `;
                gridContainer.appendChild(tagDiv);
            });
        }

        function toggleTag(tagId) {
            const index = currentFilters.tags.indexOf(tagId);
            if (index > -1) {
                currentFilters.tags.splice(index, 1);
            } else {
                currentFilters.tags.push(tagId);
            }

            updateSelectedTagsDisplay();
            applyFilters();
        }

        function updateSelectedTagsDisplay() {
            const container = document.getElementById('selected-tags');
            container.innerHTML = '';

            if (currentFilters.tags.length > 0) {
                container.classList.remove('hidden');
                currentFilters.tags.forEach(tagId => {
                    const tag = availableTags.find(t => t.id === tagId);
                    if (tag) {
                        const tagChip = document.createElement('span');
                        tagChip.className = 'inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-custom-green text-white shadow-sm';
                        tagChip.innerHTML = `
                            <span class="inline-block w-2 h-2 rounded-full mr-2 bg-white opacity-80"></span>
                            <span class="mr-2">${tag.name}</span>
                            <button onclick="removeTag(${tagId})" class="ml-1 hover:bg-white hover:bg-opacity-20 rounded-full p-0.5 transition-colors">
                                <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        `;
                        container.appendChild(tagChip);
                    }
                });
            } else {
                container.classList.add('hidden');
            }
        }

        function removeTag(tagId) {
            const index = currentFilters.tags.indexOf(tagId);
            if (index > -1) {
                currentFilters.tags.splice(index, 1);
            }

            // Update checkbox
            const checkbox = document.getElementById(`tag_${tagId}`);
            if (checkbox) {
                checkbox.checked = false;
            }

            updateSelectedTagsDisplay();
            applyFilters();
        }

        function updateAvailableTags() {
            if (tagsLoaded) {
                renderTagFilters();
            }
        }

        function clearAllFilters() {
            currentFilters = {
                search: '',
                postType: 'all',
                dateRange: 'all',
                privacy: 'all',
                tags: []
            };

            // Reset UI
            document.getElementById('post-search').value = '';
            document.getElementById('search-clear').classList.add('hidden');
            document.getElementById('date-filter').value = 'all';

            // Reset post type buttons
            setPostTypeFilter('all');

            // Reset visibility buttons
            setVisibilityFilter('all');

            // Reset tags
            updateSelectedTagsDisplay();
            if (tagsLoaded) {
                renderTagFilters();
            }

            // Hide tag filters
            const tagContainer = document.getElementById('tag-filters-container');
            if (tagContainer) {
                tagContainer.classList.add('hidden');
                document.getElementById('tag-toggle-text').textContent = 'Select Tags';
                document.getElementById('tag-toggle-icon').classList.remove('rotate-180');
            }

            // Hide filters section
            const filtersSection = document.getElementById('filters-section');
            if (filtersSection) {
                filtersSection.classList.add('hidden');
                document.getElementById('filter-toggle-text').textContent = 'Filters';
                document.getElementById('filter-toggle-icon').classList.remove('rotate-180');
                filtersVisible = false;
            }

            // Reload original posts
            location.reload();
        }

        // Load more posts for infinite scroll
        async function loadMorePosts() {
            if (isLoading || !hasMorePosts) return;

            isLoading = true;
            const loadingIndicator = document.getElementById('infinite-scroll-loading');
            const endOfFeed = document.getElementById('end-of-feed');
            const postsContainer = document.getElementById('posts-container');

            loadingIndicator.classList.remove('hidden');

            try {
                const params = new URLSearchParams();
                if (nextCursor) {
                    params.append('cursor', nextCursor);
                }

                const response = await fetch(`/api/posts/infinite-scroll?${params.toString()}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.success && data.html) {
                        // Append new posts to the container
                        postsContainer.insertAdjacentHTML('beforeend', data.html);

                        // Update cursor and hasMore status
                        nextCursor = data.next_cursor;
                        hasMorePosts = data.has_more;

                        if (!hasMorePosts) {
                            endOfFeed.classList.remove('hidden');
                        }
                    } else {
                        hasMorePosts = false;
                        endOfFeed.classList.remove('hidden');
                    }
                } else {
                    console.error('Failed to load more posts');
                }
            } catch (error) {
                console.error('Error loading more posts:', error);
            } finally {
                isLoading = false;
                loadingIndicator.classList.add('hidden');
            }
        }

        async function applyFilters() {
            // Check if any filters are active
            const hasActiveFilters = currentFilters.postType !== 'all' ||
                                   currentFilters.dateRange !== 'all' ||
                                   currentFilters.visibility !== 'all' ||
                                   currentFilters.tags.length > 0 ||
                                   currentFilters.search !== '';

            // Don't filter if no filters are active
            if (!hasActiveFilters) {
                return;
            }

            // Reset infinite scroll state when applying filters
            isLoading = false;
            hasMorePosts = true;
            nextCursor = null;

            const endOfFeedElement = document.getElementById('end-of-feed');
            if (endOfFeedElement) {
                endOfFeedElement.classList.add('hidden');
            }

            const loadingDiv = document.getElementById('filter-loading');
            const postsContainer = document.getElementById('posts-container');

            if (!loadingDiv || !postsContainer) {
                console.error('Required filter elements not found');
                return;
            }

            loadingDiv.classList.remove('hidden');

            try {
                const params = new URLSearchParams();

                // Add all filter parameters
                if (currentFilters.search) params.append('search', currentFilters.search);
                if (currentFilters.postType !== 'all') params.append('post_type', currentFilters.postType);
                if (currentFilters.dateRange !== 'all') params.append('date_range', currentFilters.dateRange);
                if (currentFilters.visibility !== 'all') params.append('visibility', currentFilters.visibility);

                // Add tags
                if (currentFilters.tags.length > 0) {
                    currentFilters.tags.forEach(tagId => {
                        params.append('tags[]', tagId);
                    });
                }

                const response = await fetch(`/posts-filter?${params.toString()}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.success) {
                        postsContainer.innerHTML = data.html;

                        // Reset infinite scroll for filtered results
                        nextCursor = data.next_cursor || null;
                        hasMorePosts = data.has_more || false;

                        // Show results count
                        console.log(`Filtered results: ${data.count} posts found`);
                    } else {
                        console.error('Filter request failed:', data);
                        postsContainer.innerHTML = '<div class="text-center py-8 text-gray-500">No posts found matching your filters.</div>';
                        hasMorePosts = false;
                    }
                } else {
                    console.error('HTTP error:', response.status);
                    postsContainer.innerHTML = '<div class="text-center py-8 text-red-500">Error loading posts. Please try again.</div>';
                }
            } catch (error) {
                console.error('Filter error:', error);
                postsContainer.innerHTML = '<div class="text-center py-8 text-red-500">Error loading posts. Please refresh the page.</div>';
            } finally {
                loadingDiv.classList.add('hidden');
            }
        }

        // Update date filter
        document.addEventListener('DOMContentLoaded', function() {
            const dateFilter = document.getElementById('date-filter');
            if (dateFilter) {
                dateFilter.addEventListener('change', function() {
                    currentFilters.dateRange = this.value;
                    applyFilters();
                });
            }
        });
    </script>









</x-layouts.unilink-layout>
