<div class="p-4 space-y-4">
    <!-- Upcoming Events -->
    <div class="bg-white rounded-lg border-opacity-20">
        <h3 class="text-lg font-bold text-custom-lightest mb-4 bg-custom-green text-custom-darkest px-4 py-4 -mx-4 -mt-4 mb-4">Upcoming Events</h3>
        <div class="space-y-3 text-sm">
            <div class="border-b border-custom-second-darkest border-opacity-20 pb-2">
                <p class="font-medium text-custom-darkest">Enrollment Period: Aug 1-15</p>
            </div>
            <div class="border-b border-custom-second-darkest border-opacity-20 pb-2">
                <p class="font-medium text-custom-darkest">Orientation Day: Aug 20</p>
            </div>
            <div class="border-b border-custom-second-darkest border-opacity-20 pb-2">
                <p class="font-medium text-custom-darkest">First Day of Classes: Aug 22</p>
            </div>
            <div>
                <p class="font-medium text-custom-darkest">Foundation Day: Sept 15</p>
            </div>
        </div>
    </div>

     <!-- Suggested Connections -->
    <?php if(auth()->guard()->check()): ?>
        <div class="livewire-stable">
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('suggested-connections', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2752033098-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/layouts/unilink-right-sidebar-content.blade.php ENDPATH**/ ?>